import Text6 from '@/components/CustomText';
import { SUPPORTED_LANGUAGES, UILanguage, changeLanguage, getCurrentLanguage } from '@/utils/locale';
import AntDesign from '@expo/vector-icons/AntDesign';
import React, { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Pressable, StyleSheet, TouchableOpacity, View } from 'react-native';

interface LanguageDropdownProps {
  iconSize?: number;
  iconColor?: string;
}

interface DropdownPosition {
  top: number;
  left: number;
}

const LanguageDropdown: React.FC<LanguageDropdownProps> = ({ iconSize = 24, iconColor = '#333' }) => {
  const { t } = useTranslation();
  const [isVisible, setIsVisible] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState<DropdownPosition>({ top: 0, left: 0 });
  const iconRef = useRef<View>(null);
  const currentLanguage = getCurrentLanguage();

  const handleIconPress = () => {
    if (iconRef.current) {
      iconRef.current.measure((_x, _y, width, height, pageX, pageY) => {
        setDropdownPosition({
          top: pageY + height + 4, // 4px below the icon
          left: pageX + width * 0.2, // slightly to the right (20% of icon width)
        });
        setIsVisible(true);
      });
    }
  };

  const handleLanguageChange = async (language: UILanguage) => {
    try {
      await changeLanguage(language);
      setIsVisible(false);
    } catch (error) {
      console.error('Failed to change language:', error);
    }
  };

  const getLanguageDisplayName = (language: UILanguage) => {
    return t(`languageOptions.${language === 'ko' ? 'korean' : 'english'}`);
  };

  return (
    <>
      <TouchableOpacity ref={iconRef} onPress={handleIconPress} style={styles.iconButton} activeOpacity={0.7}>
        <AntDesign name="earth" size={iconSize} color={iconColor} />
      </TouchableOpacity>

      {isVisible && (
        <>
          <Pressable style={styles.overlay} onPress={() => setIsVisible(false)} />
          <View style={[styles.dropdown, { position: 'absolute', top: dropdownPosition.top, left: dropdownPosition.left }]}>
            <Text6 weight="medium" style={styles.dropdownTitle}>
              {t('languageOptions.title')}
            </Text6>

            {SUPPORTED_LANGUAGES.map((language) => (
              <TouchableOpacity
                key={language}
                style={[styles.languageOption, currentLanguage === language && styles.selectedOption]}
                onPress={() => handleLanguageChange(language)}
                activeOpacity={0.7}
              >
                <Text6
                  weight={currentLanguage === language ? 'medium' : 'regular'}
                  style={[styles.languageText, currentLanguage === language && styles.selectedText]}
                >
                  {getLanguageDisplayName(language)}
                </Text6>
                {currentLanguage === language && <AntDesign name="check" size={16} color="#FB9E3A" />}
              </TouchableOpacity>
            ))}
          </View>
        </>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  iconButton: {
    padding: 4,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'transparent',
    zIndex: 999,
  },
  dropdown: {
    backgroundColor: 'white',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 20,
    minWidth: 200,
    maxWidth: 280,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
    zIndex: 1000,
  },
  dropdownTitle: {
    fontSize: 18,
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  languageOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginVertical: 2,
  },
  selectedOption: {
    backgroundColor: '#FFF5E6',
  },
  languageText: {
    fontSize: 16,
    color: '#333',
  },
  selectedText: {
    color: '#FB9E3A',
  },
});

export default LanguageDropdown;
