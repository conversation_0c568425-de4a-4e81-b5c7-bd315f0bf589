import Text6 from '@/components/CustomText';
import AntDesign from '@expo/vector-icons/AntDesign';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { StyleSheet, TextInput, TouchableOpacity, View } from 'react-native';

// Mock ingredient data for autocomplete
const MOCK_INGREDIENTS = [
  '양파',
  '당근',
  '감자',
  '토마토',
  '브로콜리',
  '시금치',
  '배추',
  '무',
  '대파',
  '마늘',
  '생강',
  '고추',
  '파프리카',
  '오이',
  '호박',
  '가지',
  '닭고기',
  '돼지고기',
  '소고기',
  '계란',
  '두부',
  '버섯',
  '콩나물',
  '숙주',
  'onion',
  'carrot',
  'potato',
  'tomato',
  'broccoli',
  'spinach',
  'cabbage',
  'radish',
  'green onion',
  'garlic',
  'ginger',
  'pepper',
  'bell pepper',
  'cucumber',
  'zucchini',
  'eggplant',
  'chicken',
  'pork',
  'beef',
  'egg',
  'tofu',
  'mushroom',
  'bean sprouts',
  'mung bean sprouts',
];

interface IngredientSearchProps {
  ingredients: string[];
  onIngredientsChange: (ingredients: string[]) => void;
  maxIngredients?: number;
  placeholder?: string;
}

const IngredientSearch: React.FC<IngredientSearchProps> = ({
  ingredients,
  onIngredientsChange,
  maxIngredients = 5,
  placeholder,
}) => {
  const { t } = useTranslation();
  const [searchText, setSearchText] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [filteredSuggestions, setFilteredSuggestions] = useState<string[]>([]);

  const defaultPlaceholder = placeholder || t('main.searchIngredients');

  const handleSearchChange = (text: string) => {
    setSearchText(text);
    if (text.trim().length > 0) {
      const filtered = MOCK_INGREDIENTS.filter((ingredient) =>
        ingredient.toLowerCase().includes(text.toLowerCase())
      ).slice(0, 8); // Limit to 8 suggestions
      setFilteredSuggestions(filtered);
      setShowSuggestions(true);
    } else {
      setShowSuggestions(false);
    }
  };

  const handleSuggestionPress = (ingredient: string) => {
    if (!ingredients.includes(ingredient) && ingredients.length < maxIngredients) {
      onIngredientsChange([...ingredients, ingredient]);
    }
    setSearchText('');
    setShowSuggestions(false);
  };

  const handleRemoveIngredient = (index: number) => {
    onIngredientsChange(ingredients.filter((_, i) => i !== index));
  };

  const handleSearch = () => {
    if (searchText.trim() && !ingredients.includes(searchText.trim()) && ingredients.length < maxIngredients) {
      onIngredientsChange([...ingredients, searchText.trim()]);
      setSearchText('');
    }
    setShowSuggestions(false);
  };

  return (
    <View style={styles.container}>
      {/* Search Row */}
      <View style={styles.searchContainer}>
        <View style={styles.searchRow}>
          <TextInput
            style={styles.searchInput}
            placeholder={defaultPlaceholder}
            value={searchText}
            onChangeText={handleSearchChange}
            onFocus={() => {
              if (searchText.trim().length > 0) {
                setShowSuggestions(true);
              }
            }}
          />
          <TouchableOpacity style={styles.searchButton} onPress={handleSearch} activeOpacity={0.8}>
            <AntDesign name="search1" size={20} color="#fff" />
          </TouchableOpacity>
        </View>

        {/* Autocomplete Suggestions */}
        {showSuggestions && filteredSuggestions.length > 0 && (
          <View style={styles.suggestionsContainer}>
            {filteredSuggestions.map((suggestion, index) => (
              <TouchableOpacity
                key={index}
                style={styles.suggestionCard}
                onPress={() => handleSuggestionPress(suggestion)}
                activeOpacity={0.7}
              >
                <Text6 style={styles.suggestionText}>{suggestion}</Text6>
              </TouchableOpacity>
            ))}
          </View>
        )}
      </View>

      {/* Selected Ingredients Display */}
      {ingredients.length > 0 && (
        <View style={styles.ingredientsContainer}>
          <View style={styles.ingredientsGrid}>
            {ingredients.map((ingredient, index) => (
              <View key={index} style={styles.ingredientBadge}>
                <Text6 style={styles.ingredientText}>{ingredient}</Text6>
                <TouchableOpacity onPress={() => handleRemoveIngredient(index)} style={styles.removeButton} activeOpacity={0.7}>
                  <AntDesign name="close" size={14} color="#666" />
                </TouchableOpacity>
              </View>
            ))}
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  searchContainer: {
    position: 'relative',
    zIndex: 1000,
  },
  searchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  searchInput: {
    flex: 1,
    height: 48,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 12,
    paddingHorizontal: 16,
    fontSize: 16,
    backgroundColor: '#fff',
    marginRight: 12,
  },
  searchButton: {
    width: 48,
    height: 48,
    backgroundColor: '#FB9E3A',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  suggestionsContainer: {
    position: 'absolute',
    top: 56,
    left: 0,
    right: 60,
    backgroundColor: '#fff',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
    maxHeight: 200,
    zIndex: 1001,
  },
  suggestionCard: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  suggestionText: {
    fontSize: 14,
    color: '#333',
  },
  ingredientsContainer: {
    marginTop: 16,
  },
  ingredientsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  ingredientBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginBottom: 8,
  },
  ingredientText: {
    fontSize: 14,
    color: '#333',
    marginRight: 6,
  },
  removeButton: {
    padding: 2,
  },
});

export default IngredientSearch;
