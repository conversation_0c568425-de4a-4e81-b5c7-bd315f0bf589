import React from 'react';
import { Text as RNText, TextProps, StyleSheet } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { getFontFamily } from '@/utils/fonts';

interface CustomTextProps extends TextProps {
  children: React.ReactNode;
  weight?: 'regular' | 'medium' | 'bold';
}

const Text6: React.FC<CustomTextProps> = ({ style, children, weight = 'regular', ...props }) => {
  const { fonts } = useTheme();
  const fontFamily = getFontFamily(fonts, weight);

  return (
    <RNText style={[styles.text, { fontFamily }, style]} {...props}>
      {children}
    </RNText>
  );
};

const styles = StyleSheet.create({
  text: {
    includeFontPadding: false,
  },
});

export default Text6;
