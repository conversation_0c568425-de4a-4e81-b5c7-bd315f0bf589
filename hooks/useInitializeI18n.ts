import { initI18n } from '@/i18n/init';
import { useEffect, useState } from 'react';

export function useInitializeI18n() {
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // Initialize i18n when the component mounts
    const initialize = async () => {
      try {
        await initI18n();
        setIsInitialized(true);
      } catch (error) {
        console.error('Failed to initialize i18n:', error);
      }
    };

    initialize();
  }, []);

  return isInitialized;
}
