import { useEffect, useState } from 'react';
import { initializeAds } from '@/utils/adManager';

/**
 * Custom hook to initialize ads when a component mounts
 * NOTE: This hook is currently not in use as we now initialize ads
 * only when the user submits their writing in the writing screen.
 * Kept for reference or potential future use.
 */
export function useInitializeAds() {
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // Initialize ads when the component mounts
    const initialize = async () => {
      try {
        await initializeAds();
        setIsInitialized(true);
      } catch (error) {
        console.error('Failed to initialize ads:', error);
      }
    };

    initialize();
  }, []);

  return isInitialized;
}
