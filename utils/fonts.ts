import { FontFamilies } from '@/contexts/ThemeContext'; 

/**
 * Font weight type for better type safety
 */
export type FontWeight = 'regular' | 'medium' | 'bold';

/**
 * Get font family based on language and weight
 * @param fonts - Font families object from theme context
 * @param weight - Font weight (regular, medium, bold)
 * @returns Font family string
 */
export const getFontFamily = (fonts: FontFamilies, weight: FontWeight = 'regular'): string => {
  return fonts[weight];
};

/**
 * Special font families that don't change based on UI language
 */
export const SPECIAL_FONTS = {
  // For ??
  poppins: {
    regular: 'Poppins-Regular',
    medium: 'Poppins-Medium',
    bold: 'Poppins-Bold',
  },
  // For ???
  geistMono: {
    regular: 'Geist-Mono-Regular',
    medium: 'Geist-Mono-Medium',
    bold: 'Geist-Mono-Bold',
  },
  // For ???
  gowunBatang: {
    regular: 'Gowun-Batang-Regular',
    bold: 'Gowun-Batang-Bold',
  },
} as const;

/**
 * Get special font family for components that don't use theme fonts
 * @param fontType - Type of special font
 * @param weight - Font weight
 * @returns Font family string
 */
export const getSpecialFont = (fontType: keyof typeof SPECIAL_FONTS, weight: keyof (typeof SPECIAL_FONTS)[typeof fontType]): string => {
  return SPECIAL_FONTS[fontType][weight];
};
