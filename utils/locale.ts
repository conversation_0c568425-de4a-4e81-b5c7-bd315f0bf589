import i18n from 'i18next';
import { saveUILanguage } from './storage';

export type UILanguage = 'ko' | 'en';
// List of UI languages supported by the app
export const SUPPORTED_LANGUAGES = ['ko', 'en'] as UILanguage[];

export const getLanguageName = (language: UILanguage) => {
  switch (language) {
    case 'ko':
      return '한국어';
    case 'en':
      return 'English';
  }
};

export const getCurrentLanguageName = () => {
  return getLanguageName(getCurrentLanguage());
};

export const getCurrentLanguage = () => {
  return i18n.language as UILanguage;
};

export const changeLanguage = async (language: UILanguage) => {
  try {
    await i18n.changeLanguage(language);
    await saveUILanguage(language);
  } catch (error) {
    console.error('Error changing language:', error);
    throw error;
  }
};
