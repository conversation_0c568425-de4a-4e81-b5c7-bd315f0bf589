import Text6 from '@/components/CustomText';
import IngredientSearch from '@/components/IngredientSearch';
import LanguageDropdown from '@/components/LanguageDropdown';

import AntDesign from '@expo/vector-icons/AntDesign';
import { router } from 'expo-router';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';

export default function MainScreen() {
  const { t } = useTranslation();
  const [ingredients, setIngredients] = useState<string[]>([]);

  const handleBookPress = () => {
    router.push('/recipe_list');
  };

  return (
    <View style={styles.container}>
      {/* Top row with two icons spaced between */}
      <View style={styles.topRow}>
        <LanguageDropdown iconSize={24} iconColor="#333" />
        <TouchableOpacity onPress={handleBookPress} activeOpacity={0.7}>
          <AntDesign name="book" size={24} color="#333" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        {/* Header Text */}
        <View style={styles.headerContainer}>
          <Text6 weight="medium" style={styles.taglineText}>
            {t('main.tagline')}
          </Text6>
        </View>

        {/* Ingredient Search */}
        <View style={styles.searchContainer}>
          <IngredientSearch ingredients={ingredients} onIngredientsChange={setIngredients} maxIngredients={5} />
        </View>

        {/* Spacer for ad banner */}
        <View style={styles.contentSpacer} />
      </ScrollView>

      {/* Google Ads Banner */}
      <View style={styles.adBanner}>
        <Text6 style={styles.adPlaceholder}>Google Ads Banner</Text6>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  topRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60, // Safe area padding for status bar
    paddingBottom: 20,
  },
  scrollContainer: {
    flex: 1,
  },
  headerContainer: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    alignItems: 'center',
  },
  taglineText: {
    fontSize: 24,
    color: '#333',
    textAlign: 'center',
    lineHeight: 32,
  },
  searchContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
    position: 'relative',
    zIndex: 1000,
  },
  searchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 12,
    color: '#333',
  },
  searchButton: {
    backgroundColor: '#FB9E3A',
    borderRadius: 8,
    padding: 10,
    marginLeft: 8,
  },
  suggestionsContainer: {
    position: 'absolute',
    top: 60,
    left: 20,
    right: 20,
    backgroundColor: 'white',
    borderRadius: 12,
    maxHeight: 200,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
    zIndex: 1001,
  },
  suggestionItem: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    backgroundColor: 'white',
    borderRadius: 8,
    marginHorizontal: 4,
    marginVertical: 2,
  },
  suggestionText: {
    fontSize: 16,
    color: '#333',
  },
  ingredientsContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  ingredientsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  ingredientBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF5E6',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#FB9E3A',
  },
  ingredientText: {
    fontSize: 14,
    color: '#FB9E3A',
    marginRight: 6,
  },
  removeButton: {
    padding: 2,
  },
  contentSpacer: {
    height: 100, // Space for ad banner
  },
  adBanner: {
    height: 80,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  adPlaceholder: {
    fontSize: 16,
    color: '#666',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'transparent',
    zIndex: 999,
  },
});
