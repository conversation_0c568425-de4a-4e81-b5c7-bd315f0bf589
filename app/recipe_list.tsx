import Text6 from '@/components/CustomText';
import { DifficultyLevel, Recipe, getRecipes } from '@/utils/storage';
import AntDesign from '@expo/vector-icons/AntDesign';
import { router, useFocusEffect } from 'expo-router';
import React, { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList, Image, SafeAreaView, StyleSheet, TouchableOpacity, View } from 'react-native';

const getDifficultyColor = (difficulty: DifficultyLevel): string => {
  switch (difficulty) {
    case 'Beginner':
      return '#4CAF50';
    case 'Intermediate':
      return '#FF9800';
    case 'Advanced':
      return '#F44336';
    case 'Chef':
      return '#9C27B0';
    default:
      return '#757575';
  }
};

interface RecipeTileProps {
  recipe: Recipe;
  onPress: () => void;
}

const RecipeTile: React.FC<RecipeTileProps> = ({ recipe, onPress }) => {
  const { t } = useTranslation();

  return (
    <TouchableOpacity style={styles.recipeTile} onPress={onPress} activeOpacity={0.7}>
      {/* Recipe Image */}
      <View style={styles.imageContainer}>
        {recipe.imageUri ? (
          <Image source={{ uri: recipe.imageUri }} style={styles.recipeImage} />
        ) : (
          <View style={styles.placeholderImage}>
            <AntDesign name="picture" size={40} color="#ccc" />
          </View>
        )}
      </View>

      {/* Recipe Info */}
      <View style={styles.recipeInfo}>
        <Text6 weight="medium" style={styles.recipeTitle} numberOfLines={2}>
          {recipe.title}
        </Text6>

        {/* Difficulty Badge */}
        <View style={[styles.difficultyBadge, { backgroundColor: getDifficultyColor(recipe.difficulty) }]}>
          <Text6 weight="medium" style={styles.difficultyText}>
            {t(`recipeEdit.difficultyLevels.${recipe.difficulty}`)}
          </Text6>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default function RecipeListScreen() {
  const { t } = useTranslation();
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [loading, setLoading] = useState(true);

  const loadRecipes = useCallback(async () => {
    try {
      setLoading(true);
      const savedRecipes = await getRecipes();
      setRecipes(savedRecipes);
    } catch (error) {
      console.error('Error loading recipes:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  useFocusEffect(
    useCallback(() => {
      loadRecipes();
    }, [loadRecipes])
  );

  const handleBackPress = () => {
    router.back();
  };

  const handleRecipePress = (recipe: Recipe) => {
    router.push(`/recipe_detail?id=${recipe.id}`);
  };

  const handleAddRecipe = () => {
    router.push('/recipe_edit');
  };

  const renderRecipe = ({ item }: { item: Recipe }) => <RecipeTile recipe={item} onPress={() => handleRecipePress(item)} />;

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBackPress} style={styles.backButton} activeOpacity={0.7}>
          <AntDesign name="arrowleft" size={24} color="#333" />
        </TouchableOpacity>
        <Text6 weight="medium" style={styles.headerTitle}>
          {t('recipeList.title')}
        </Text6>
        <TouchableOpacity onPress={handleAddRecipe} style={styles.addButton} activeOpacity={0.7}>
          <AntDesign name="plus" size={24} color="#FB9E3A" />
        </TouchableOpacity>
      </View>

      {/* Recipe List */}
      <View style={styles.listContainer}>
        {loading ? (
          <View style={styles.centerContent}>
            <Text6 style={styles.loadingText}>Loading...</Text6>
          </View>
        ) : recipes.length === 0 ? (
          <View style={styles.centerContent}>
            <AntDesign name="book" size={64} color="#ccc" style={styles.emptyIcon} />
            <Text6 style={styles.noRecipesText}>{t('recipeList.noRecipes')}</Text6>
            <TouchableOpacity style={styles.addRecipeButton} onPress={handleAddRecipe} activeOpacity={0.8}>
              <Text6 weight="medium" style={styles.addRecipeButtonText}>
                {t('recipeEdit.newRecipeTitle')}
              </Text6>
            </TouchableOpacity>
          </View>
        ) : (
          <FlatList
            data={recipes}
            renderItem={renderRecipe}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContent}
            showsVerticalScrollIndicator={false}
          />
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    color: '#333',
  },
  addButton: {
    padding: 4,
  },
  listContainer: {
    flex: 1,
  },
  listContent: {
    padding: 16,
  },
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
  emptyIcon: {
    marginBottom: 16,
  },
  noRecipesText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  addRecipeButton: {
    backgroundColor: '#FB9E3A',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  addRecipeButtonText: {
    color: '#fff',
    fontSize: 16,
  },
  recipeTile: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
  },
  imageContainer: {
    width: '100%',
    height: 200,
  },
  recipeImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  recipeInfo: {
    padding: 16,
  },
  recipeTitle: {
    fontSize: 18,
    color: '#333',
    marginBottom: 8,
  },
  difficultyBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  difficultyText: {
    color: '#fff',
    fontSize: 12,
  },
});
