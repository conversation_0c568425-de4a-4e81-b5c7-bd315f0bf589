import Text6 from '@/components/CustomText';
import IngredientSearch from '@/components/IngredientSearch';
import { DifficultyLevel, Recipe, getRecipeById, saveRecipe, updateRecipe } from '@/utils/storage';
import AntDesign from '@expo/vector-icons/AntDesign';

import { router, useLocalSearchParams } from 'expo-router';
import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Alert, Modal, SafeAreaView, ScrollView, StyleSheet, TextInput, TouchableOpacity, View } from 'react-native';

interface RecipeStep {
  id: string;
  text: string;
}

export default function RecipeEditScreen() {
  const { t } = useTranslation();
  const { id } = useLocalSearchParams<{ id?: string }>();

  const [recipeName, setRecipeName] = useState('');
  const [ingredients, setIngredients] = useState<string[]>([]);
  const [difficulty, setDifficulty] = useState<DifficultyLevel>('Beginner');
  const [steps, setSteps] = useState<RecipeStep[]>([{ id: '1', text: '' }]);
  const [loading, setLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [showDifficultyModal, setShowDifficultyModal] = useState(false);

  const loadRecipe = useCallback(async () => {
    if (!id) return;

    try {
      setLoading(true);
      const recipe = await getRecipeById(id);
      if (recipe) {
        setRecipeName(recipe.title);
        setIngredients(recipe.ingredients);
        setDifficulty(recipe.difficulty);
        setSteps(recipe.steps.map((step, index) => ({ id: (index + 1).toString(), text: step })));
        setIsEditing(true);
      }
    } catch (error) {
      console.error('Error loading recipe:', error);
      Alert.alert(t('main.errorTitle'), t('main.networkError'));
    } finally {
      setLoading(false);
    }
  }, [id, t]);

  useEffect(() => {
    if (id) {
      loadRecipe();
    }
  }, [id, loadRecipe]);

  const handleBackPress = () => {
    router.back();
  };

  const handleAddStep = () => {
    const newStep: RecipeStep = {
      id: (steps.length + 1).toString(),
      text: '',
    };
    setSteps([...steps, newStep]);
  };

  const handleRemoveStep = (stepId: string) => {
    if (steps.length > 1) {
      setSteps(steps.filter((step) => step.id !== stepId));
    }
  };

  const handleStepChange = (stepId: string, text: string) => {
    setSteps(steps.map((step) => (step.id === stepId ? { ...step, text } : step)));
  };

  const validateForm = (): boolean => {
    if (!recipeName.trim()) {
      Alert.alert(t('writeRecipe.requiredFieldsTitle'), t('writeRecipe.requiredFieldsMessage'));
      return false;
    }

    if (ingredients.length === 0) {
      Alert.alert(t('writeRecipe.requiredFieldsTitle'), t('writeRecipe.requiredFieldsMessage'));
      return false;
    }

    const hasValidSteps = steps.some((step) => step.text.trim().length > 0);
    if (!hasValidSteps) {
      Alert.alert(t('writeRecipe.requiredFieldsTitle'), t('writeRecipe.requiredFieldsMessage'));
      return false;
    }

    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);

      const validSteps = steps.filter((step) => step.text.trim().length > 0).map((step) => step.text.trim());

      if (isEditing && id) {
        // Update existing recipe
        await updateRecipe(id, {
          title: recipeName.trim(),
          ingredients,
          difficulty,
          steps: validSteps,
          updatedAt: Date.now(),
        });
      } else {
        // Create new recipe
        const newRecipe: Recipe = {
          id: Date.now().toString(),
          title: recipeName.trim(),
          ingredients,
          difficulty,
          steps: validSteps,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        };

        await saveRecipe(newRecipe);
      }

      router.back();
    } catch (error) {
      console.error('Error saving recipe:', error);
      Alert.alert(t('main.errorTitle'), t('main.networkError'));
    } finally {
      setLoading(false);
    }
  };

  const difficultyOptions: DifficultyLevel[] = ['Beginner', 'Intermediate', 'Advanced', 'Chef'];

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBackPress} style={styles.backButton} activeOpacity={0.7}>
          <AntDesign name="arrowleft" size={24} color="#333" />
        </TouchableOpacity>
        <Text6 weight="medium" style={styles.headerTitle}>
          {isEditing ? t('recipeEdit.title') : t('recipeEdit.newRecipeTitle')}
        </Text6>
        <TouchableOpacity onPress={handleSave} style={styles.saveButton} activeOpacity={0.7} disabled={loading}>
          <Text6 weight="medium" style={[styles.saveButtonText, loading && styles.disabledText]}>
            {t('common.save')}
          </Text6>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Image Container Placeholder */}
        <TouchableOpacity style={styles.imageContainer} activeOpacity={0.7}>
          <AntDesign name="camera" size={40} color="#ccc" />
          <Text6 style={styles.imageText}>{t('recipeEdit.addImagePlaceholder')}</Text6>
        </TouchableOpacity>

        {/* Recipe Name */}
        <View style={styles.section}>
          <Text6 weight="medium" style={styles.sectionTitle}>
            {t('recipeEdit.recipeName')}
          </Text6>
          <TextInput
            style={styles.textInput}
            placeholder={t('recipeEdit.recipeNamePlaceholder')}
            value={recipeName}
            onChangeText={setRecipeName}
          />
        </View>

        {/* Ingredients */}
        <View style={styles.section}>
          <Text6 weight="medium" style={styles.sectionTitle}>
            {t('common.ingredients')}
          </Text6>
          <IngredientSearch
            ingredients={ingredients}
            onIngredientsChange={setIngredients}
            maxIngredients={10}
            placeholder={t('recipeForm.searchIngredients')}
          />
        </View>

        {/* Difficulty */}
        <View style={styles.section}>
          <Text6 weight="medium" style={styles.sectionTitle}>
            {t('recipeEdit.difficulty')}
          </Text6>
          <TouchableOpacity style={styles.difficultySelector} onPress={() => setShowDifficultyModal(true)} activeOpacity={0.7}>
            <Text6 style={styles.difficultyText}>{t(`recipeEdit.difficultyLevels.${difficulty}`)}</Text6>
            <AntDesign name="down" size={16} color="#666" />
          </TouchableOpacity>
        </View>

        {/* Recipe Steps */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text6 weight="medium" style={styles.sectionTitle}>
              {t('recipeForm.cookingSteps')}
            </Text6>
            <TouchableOpacity onPress={handleAddStep} style={styles.addStepButton} activeOpacity={0.7}>
              <AntDesign name="plus" size={20} color="#FB9E3A" />
              <Text6 weight="medium" style={styles.addStepText}>
                {t('recipeEdit.addStep')}
              </Text6>
            </TouchableOpacity>
          </View>

          {steps.map((step, index) => (
            <View key={step.id} style={styles.stepContainer}>
              <View style={styles.stepHeader}>
                <Text6 weight="medium" style={styles.stepNumber}>
                  {index + 1}
                </Text6>
                {steps.length > 1 && (
                  <TouchableOpacity onPress={() => handleRemoveStep(step.id)} style={styles.removeStepButton} activeOpacity={0.7}>
                    <AntDesign name="close" size={16} color="#F44336" />
                  </TouchableOpacity>
                )}
              </View>
              <TextInput
                style={styles.stepInput}
                placeholder={t('recipeEdit.stepPlaceholder')}
                value={step.text}
                onChangeText={(text) => handleStepChange(step.id, text)}
                multiline
                numberOfLines={3}
              />
            </View>
          ))}
        </View>

        {/* Bottom Spacer */}
        <View style={styles.bottomSpacer} />
      </ScrollView>

      {/* Difficulty Selection Modal */}
      <Modal visible={showDifficultyModal} transparent={true} animationType="fade" onRequestClose={() => setShowDifficultyModal(false)}>
        <TouchableOpacity style={styles.modalOverlay} activeOpacity={1} onPress={() => setShowDifficultyModal(false)}>
          <View style={styles.modalContent}>
            <Text6 weight="medium" style={styles.modalTitle}>
              {t('recipeEdit.difficulty')}
            </Text6>
            {difficultyOptions.map((option) => (
              <TouchableOpacity
                key={option}
                style={[styles.difficultyOption, difficulty === option && styles.selectedDifficultyOption]}
                onPress={() => {
                  setDifficulty(option);
                  setShowDifficultyModal(false);
                }}
                activeOpacity={0.7}
              >
                <Text6
                  weight={difficulty === option ? 'medium' : 'regular'}
                  style={[styles.difficultyOptionText, difficulty === option && styles.selectedDifficultyOptionText]}
                >
                  {t(`recipeEdit.difficultyLevels.${option}`)}
                </Text6>
                {difficulty === option && <AntDesign name="check" size={16} color="#FB9E3A" />}
              </TouchableOpacity>
            ))}
          </View>
        </TouchableOpacity>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    color: '#333',
    flex: 1,
    textAlign: 'center',
  },
  saveButton: {
    padding: 4,
  },
  saveButtonText: {
    fontSize: 16,
    color: '#FB9E3A',
  },
  disabledText: {
    color: '#ccc',
  },
  content: {
    flex: 1,
  },
  imageContainer: {
    height: 200,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 20,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#ddd',
    borderStyle: 'dashed',
  },
  imageText: {
    fontSize: 16,
    color: '#999',
    marginTop: 8,
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    color: '#333',
    marginBottom: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  difficultySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
  },
  difficultyText: {
    fontSize: 16,
    color: '#333',
  },
  addStepButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    backgroundColor: '#FFF5E6',
  },
  addStepText: {
    fontSize: 14,
    color: '#FB9E3A',
    marginLeft: 4,
  },
  stepContainer: {
    marginBottom: 16,
  },
  stepHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  stepNumber: {
    fontSize: 16,
    color: '#FB9E3A',
    backgroundColor: '#FFF5E6',
    width: 28,
    height: 28,
    borderRadius: 14,
    textAlign: 'center',
    lineHeight: 28,
  },
  removeStepButton: {
    padding: 4,
  },
  stepInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: '#fff',
    minHeight: 80,
    textAlignVertical: 'top',
  },
  bottomSpacer: {
    height: 40,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    minWidth: 280,
    maxWidth: '80%',
  },
  modalTitle: {
    fontSize: 18,
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  difficultyOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  selectedDifficultyOption: {
    backgroundColor: '#FFF5E6',
  },
  difficultyOptionText: {
    fontSize: 16,
    color: '#333',
  },
  selectedDifficultyOptionText: {
    color: '#FB9E3A',
  },
});
