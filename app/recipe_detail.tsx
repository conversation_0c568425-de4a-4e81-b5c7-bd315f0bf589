import Text6 from '@/components/CustomText';
import { DifficultyLevel, Recipe, deleteRecipe, getRecipeById } from '@/utils/storage';
import AntDesign from '@expo/vector-icons/AntDesign';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Alert, Image, SafeAreaView, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';

const getDifficultyColor = (difficulty: DifficultyLevel): string => {
  switch (difficulty) {
    case 'Beginner':
      return '#4CAF50';
    case 'Intermediate':
      return '#FF9800';
    case 'Advanced':
      return '#F44336';
    case 'Chef':
      return '#9C27B0';
    default:
      return '#757575';
  }
};

export default function RecipeDetailScreen() {
  const { t } = useTranslation();
  const { id } = useLocalSearchParams<{ id: string }>();
  const [recipe, setRecipe] = useState<Recipe | null>(null);
  const [loading, setLoading] = useState(true);

  const loadRecipe = useCallback(async () => {
    if (!id) {
      router.back();
      return;
    }

    try {
      setLoading(true);
      const savedRecipe = await getRecipeById(id);
      if (savedRecipe) {
        setRecipe(savedRecipe);
      } else {
        Alert.alert(t('main.errorTitle'), 'Recipe not found');
        router.back();
      }
    } catch (error) {
      console.error('Error loading recipe:', error);
      Alert.alert(t('main.errorTitle'), t('main.networkError'));
      router.back();
    } finally {
      setLoading(false);
    }
  }, [id, t]);

  useEffect(() => {
    loadRecipe();
  }, [loadRecipe]);

  const handleBackPress = () => {
    router.back();
  };

  const handleEditPress = () => {
    if (recipe) {
      router.push(`/recipe_edit?id=${recipe.id}`);
    }
  };

  const handleDeletePress = () => {
    if (!recipe) return;

    Alert.alert(t('recipeDetail.deleteRecipe'), 'Are you sure you want to delete this recipe?', [
      {
        text: t('common.no'),
        style: 'cancel',
      },
      {
        text: t('common.delete'),
        style: 'destructive',
        onPress: async () => {
          try {
            await deleteRecipe(recipe.id);
            router.back();
          } catch (error) {
            console.error('Error deleting recipe:', error);
            Alert.alert(t('main.errorTitle'), t('main.networkError'));
          }
        },
      },
    ]);
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.centerContent}>
          <Text6 style={styles.loadingText}>Loading...</Text6>
        </View>
      </SafeAreaView>
    );
  }

  if (!recipe) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.centerContent}>
          <Text6 style={styles.errorText}>Recipe not found</Text6>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBackPress} style={styles.backButton} activeOpacity={0.7}>
          <AntDesign name="arrowleft" size={24} color="#333" />
        </TouchableOpacity>
        <View style={styles.headerActions}>
          <TouchableOpacity onPress={handleEditPress} style={styles.actionButton} activeOpacity={0.7}>
            <AntDesign name="edit" size={20} color="#FB9E3A" />
          </TouchableOpacity>
          <TouchableOpacity onPress={handleDeletePress} style={styles.actionButton} activeOpacity={0.7}>
            <AntDesign name="delete" size={20} color="#F44336" />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Recipe Image */}
        <View style={styles.imageContainer}>
          {recipe.imageUri ? (
            <Image source={{ uri: recipe.imageUri }} style={styles.recipeImage} />
          ) : (
            <View style={styles.placeholderImage}>
              <AntDesign name="picture" size={60} color="#ccc" />
            </View>
          )}
        </View>

        {/* Recipe Info */}
        <View style={styles.infoSection}>
          <Text6 weight="bold" style={styles.recipeTitle}>
            {recipe.title}
          </Text6>

          {/* Difficulty Badge */}
          <View style={[styles.difficultyBadge, { backgroundColor: getDifficultyColor(recipe.difficulty) }]}>
            <Text6 weight="medium" style={styles.difficultyText}>
              {t(`recipeEdit.difficultyLevels.${recipe.difficulty}`)}
            </Text6>
          </View>
        </View>

        {/* Ingredients */}
        <View style={styles.section}>
          <Text6 weight="medium" style={styles.sectionTitle}>
            {t('common.ingredients')}
          </Text6>
          <View style={styles.ingredientsGrid}>
            {recipe.ingredients.map((ingredient, index) => (
              <View key={index} style={styles.ingredientBadge}>
                <Text6 style={styles.ingredientText}>{ingredient}</Text6>
              </View>
            ))}
          </View>
        </View>

        {/* Recipe Steps */}
        <View style={styles.section}>
          <Text6 weight="medium" style={styles.sectionTitle}>
            {t('recipeForm.cookingSteps')}
          </Text6>
          {recipe.steps.map((step, index) => (
            <View key={index} style={styles.stepContainer}>
              <View style={styles.stepNumber}>
                <Text6 weight="medium" style={styles.stepNumberText}>
                  {index + 1}
                </Text6>
              </View>
              <Text6 style={styles.stepText}>{step}</Text6>
            </View>
          ))}
        </View>

        {/* Bottom Spacer */}
        <View style={styles.bottomSpacer} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
  errorText: {
    fontSize: 16,
    color: '#F44336',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  backButton: {
    padding: 4,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    padding: 8,
    marginLeft: 8,
  },
  content: {
    flex: 1,
  },
  imageContainer: {
    width: '100%',
    height: 250,
  },
  recipeImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  infoSection: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  recipeTitle: {
    fontSize: 24,
    color: '#333',
    marginBottom: 12,
  },
  difficultyBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  difficultyText: {
    color: '#fff',
    fontSize: 14,
  },
  section: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    color: '#333',
    marginBottom: 16,
  },
  ingredientsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  ingredientBadge: {
    backgroundColor: '#FFF5E6',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#FB9E3A',
  },
  ingredientText: {
    fontSize: 14,
    color: '#FB9E3A',
  },
  stepContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    alignItems: 'flex-start',
  },
  stepNumber: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#FB9E3A',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    marginTop: 2,
  },
  stepNumberText: {
    color: '#fff',
    fontSize: 14,
  },
  stepText: {
    flex: 1,
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
  },
  bottomSpacer: {
    height: 40,
  },
});
