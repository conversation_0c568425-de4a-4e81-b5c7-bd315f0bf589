// i18n/index.ts
import i18n from 'i18next';
import * as ExpoLocalization from 'expo-localization';
import { initReactI18next } from 'react-i18next';

// Import translation files
import en from './locales/en.json';
import ko from './locales/ko.json';
import { getUILanguage } from '@/utils/storage';

export const initI18n = async () => {
  console.log('Calling initI18n');
  let savedLanguage: string | null = await getUILanguage();
  if (!savedLanguage) {
    savedLanguage = ExpoLocalization.getLocales()[0].languageCode || 'ko';
  }

  i18n
    .use(initReactI18next) // passes i18n down to react-i18next
    .init({
      resources: {
        en: { translation: en },
        ko: { translation: ko },
      },
      lng: savedLanguage,
      fallbackLng: {
        'en-*': ['en'],
        'ko-*': ['ko'],
        default: ['ko', 'en'],
      },
      interpolation: {
        escapeValue: false, // React already safes from xss
      },
    });
};

export default i18n;
