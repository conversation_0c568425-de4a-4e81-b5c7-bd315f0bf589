# Project

This project is a modern (2025) React Native project that uses Expo. Our product is a recipe generation app using LLMs. It supports iOS and Android (no web support).

# Collaboration

Always focus on your current task or ticket, rather than fixing unrelated pre-existing type errors.
Why? Your colleagues may already be working on those errors in other branches, us working on them would cause merge errors.

# When using MCP tools

Linear team id: 4875bb27-a2c9-4490-8d86-58974a684e5a

# Development Practices

- React hooks must always be called at the top level of components before any conditional returns to prevent 'change in the order of hooks' errors.
- Avoid using string pattern matching (like startsWith) for type checking; prefer creating dedicated utility functions like getAttemptsByQuestionType for more robust type-based filtering.
- When running tests, use non-watch mode (without --watchAll flag) so the output can be properly read and analyzed.
- Use ripgrep (rg) command to search for code patterns across the codebase, with syntax like `rg "pattern" -g "*.tsx"`.

# UI/UX Preferences

- Ensure components adhere to contemporary 2025 design trends for optimal user experience.
- Always use <Text6> rather than <Text> unless explicitly noted.
- For Text6 components, use the weight property instead of fontWeight in styles.
