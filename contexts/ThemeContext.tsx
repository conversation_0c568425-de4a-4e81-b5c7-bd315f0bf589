import React, { createContext, useContext, ReactNode } from 'react';
import { useTranslation } from 'react-i18next';

export interface FontFamilies {
  regular: string;
  medium: string;
  bold: string;
}

export interface ThemeContextType {
  fonts: FontFamilies;
  isKorean: boolean;
}

const ThemeContext = createContext<ThemeContextType | null>(null);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  // Check if current language is Korean
  // Here we use useTranslation rather than "getCurrentLanguage" to make this refresh on lang change
  const { i18n } = useTranslation();
  const isKorean = i18n.language === 'ko';

  // Define font families based on language
  const fonts: FontFamilies = isKorean
    ? {
        regular: 'NotoSansKR-Regular',
        medium: 'NotoSansKR-Medium',
        bold: 'NotoSansKR-Bold',
      }
    : {
        regular: 'Outfit-Regular',
        medium: 'Outfit-Medium',
        bold: 'Outfit-Bold',
      };

  const value: ThemeContextType = {
    fonts,
    isKorean,
  };

  return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>;
};
