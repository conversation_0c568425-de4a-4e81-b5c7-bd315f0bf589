# dependencies
node_modules/

# expo
.expo/
dist/
web-build/
expo-env.d.ts

# native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macos
.DS_Store
*.pem

# local env files
.env*.local
.env

# typescript
*.tsbuildinfo

# Contains auth credits
.npmrc
.npmrc.local

/android
/ios

# App bundles
*.aab
*.ipa
i18n.cache

# Fastlane sensitive files
fastlane/AuthKey_*.p8
fastlane/google-play-service-account.json
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots
fastlane/test_output

# Google Services files (use EAS file environment variables instead)
google-services.json
GoogleService-Info.plist
