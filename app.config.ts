import { ConfigContext, ExpoConfig } from 'expo/config';
import 'ts-node/register';

export default ({ config }: ConfigContext): ExpoConfig => ({
  name: '냉털',
  slug: 'leftovers',
  version: '1.1.0',
  orientation: 'portrait',
  icon: './assets/images/icon.png',
  scheme: 'leftovers',
  userInterfaceStyle: 'automatic',
  newArchEnabled: true,
  extra: {
    eas: {
      projectId: '08ad3d54-c222-4bd5-9973-f777e45cbcd7',
    },
  },
  ios: {
    supportsTablet: true,
    bundleIdentifier: 'com.odupangjjeong.mychef',
    buildNumber: '4',
    config: {
      googleMobileAdsAppId: 'ca-app-pub-2947221843793912~3818161788',
    },
    infoPlist: {
      ITSAppUsesNonExemptEncryption: false,
    },
  },
  android: {
    package: 'com.odupangjjeong.mychef',
    versionCode: 4,
    adaptiveIcon: {
      foregroundImage: './assets/images/adaptive-icon.png',
      backgroundColor: '#ffffff',
    },
    edgeToEdgeEnabled: true,
    config: {
      googleMobileAdsAppId: 'ca-app-pub-2947221843793912~1986821972',
    },
    intentFilters: [
      {
        action: 'VIEW',
        autoVerify: true,
        data: [
          {
            scheme: 'mychef',
            host: 'mychef.com',
          },
        ],
        category: ['BROWSABLE', 'DEFAULT'],
      },
    ],
    permissions: ['INTERNET'],
  },
  web: {
    bundler: 'metro',
    output: 'single',
    favicon: './assets/images/favicon.png',
  },
  plugins: [
    'expo-router',
    [
      'expo-splash-screen',
      {
        image: './assets/images/splash-icon.png',
        imageWidth: 200,
        resizeMode: 'contain',
        backgroundColor: '#ffffff',
      },
    ],
    [
      'react-native-google-mobile-ads',
      {
        androidAppId: 'ca-app-pub-2947221843793912~1986821972',
        android_app_id: 'ca-app-pub-2947221843793912~1986821972',
        iosAppId: 'ca-app-pub-2947221843793912~3818161788',
        ios_app_id: 'ca-app-pub-2947221843793912~3818161788',
        delayAppMeasurementInit: true,
        delay_app_measurement_init: true,
        userTrackingUsageDescription: 'This only lets us make any potential ads more relevant to you.',
        user_tracking_usage_description: 'This only lets us make any potential ads more relevant to you.',
      },
    ],
    [
      'expo-tracking-transparency',
      {
        userTrackingPermission: 'This only lets us make any potential ads more relevant to you.',
        user_tracking_permission: 'This only lets us make any potential ads more relevant to you.',
      },
    ],
    [
      'expo-build-properties',
      {
        android: {
          enableProguardInReleaseBuilds: true,
        },
      },
    ],
  ],
});
